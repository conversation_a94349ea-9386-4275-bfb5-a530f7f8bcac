query UserActivity($first: Int = 4, $skip: Int = 0, $userId: String!) {
    ideasCreated: ideas(
        first: $first,
        skip: $skip,
        where: { creator: $userId }
    ) {
        ...IdeaFields
    }
    ideasFunded: ideaContributions(
        first: $first,
        skip: $skip,
        where: { funder: $userId },
        orderBy: createdTime,
        orderDirection: desc
    ) {
        idea {
            ...IdeaFieldsDetailed
        }
        contribution
        createdTime
    }
    solutionsFunded: solutionContributions(
        first: $first,
        skip: $skip,
        where: { funder: $userId },
        orderBy: createdTime,
        orderDirection: desc
    ) {
        solution {
            ...SolutionFieldsDetailed
        }
        contribution
        createdTime
    }
    solutionsDrafted: solutions(
        first: $first,
        skip: $skip,
        where: { drafter: $userId },
        orderBy: startTime,
        orderDirection: desc
    ) {
        ...SolutionFields
    }
}